import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/src/features/auth/lib/auth-config";
import client from "@/src/lib/database/client";
import { getGitHubAccountInfo } from "@/src/lib/auth/github-org-utils";

/**
 * Test endpoint to verify GitHub org ID is saved in accounts table
 */
export async function GET(request: NextRequest) {
  try {
    // Get authenticated session
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ 
        error: "Not authenticated" 
      }, { status: 401 });
    }

    // Connect to MongoDB
    await client.connect();
    
    // Get GitHub account information
    const githubAccountInfo = await getGitHubAccountInfo(client, session.user.id);
    
    if (!githubAccountInfo) {
      return NextResponse.json({
        message: "No GitHub account found for user",
        userId: session.user.id
      });
    }

    // Also fetch raw account data for comparison
    const db = client.db('test');
    const accountsCollection = db.collection('accounts');
    const rawAccount = await accountsCollection.findOne({
      userId: session.user.id,
      provider: 'github'
    });

    return NextResponse.json({
      message: "GitHub account information retrieved successfully",
      user: {
        id: session.user.id,
        name: session.user.name,
        email: session.user.email,
        githubUsername: session.user.githubUsername
      },
      githubAccountInfo,
      rawAccountData: {
        provider: rawAccount?.provider,
        providerAccountId: rawAccount?.providerAccountId,
        githubOrgId: rawAccount?.githubOrgId,
        githubUsername: rawAccount?.githubUsername,
        hasAccessToken: !!rawAccount?.access_token,
        createdAt: rawAccount?.createdAt,
        updatedAt: rawAccount?.updatedAt
      }
    });

  } catch (error) {
    console.error("Error in GitHub org test endpoint:", error);
    return NextResponse.json({ 
      error: "Internal server error",
      message: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
