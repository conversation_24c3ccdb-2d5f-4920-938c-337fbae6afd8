import { useQuery } from "@tanstack/react-query";

export interface Organization {
  id: string;
  name: string;
  provider: 'github' | 'gitlab';
  avatar?: string;
  isCurrent?: boolean;
  type: 'personal' | 'organization';
  description?: string;
  publicRepos?: number;
  path?: string;
}

export interface OrganizationsResponse {
  organizations: Organization[];
  totalCount: number;
  missingProviders: string[];
  errors?: Array<{
    provider: string;
    error: string;
  }>;
  user: {
    id: string;
    email?: string;
    name?: string;
  };
}

export interface OrganizationsError {
  message: string;
  missingProviders?: string[];
  requiresVCSConnection?: boolean;
}

export function useOrganizations() {
  return useQuery<OrganizationsResponse, OrganizationsError>({
    queryKey: ['organizations'],
    queryFn: async () => {
      const response = await fetch("/api/organizations");
      const data = await response.json();

      if (!response.ok) {
        const error: OrganizationsError = {
          message: data.message || `Failed to fetch organizations: ${response.statusText}`,
          missingProviders: data.missingProviders,
          requiresVCSConnection: data.missingProviders?.length > 0,
        };
        throw error;
      }

      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      if (error?.requiresVCSConnection) {
        return false;
      }
      return failureCount < 3;
    }
  });
}
